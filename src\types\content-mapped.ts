/**
 * Shared content-mapped types and runtime type guards
 * These refine any-typed fields from Contentlayer generated types.
 */

export interface BlogSEO {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  noindex?: boolean;
}

export interface BlogImage {
  src: string;
  alt: string;
  caption?: string;
}

export interface BlogAuthorSocial {
  twitter?: string;
  linkedin?: string;
  email?: string;
}

export function isRecord(x: unknown): x is Record<string, unknown> {
  return typeof x === "object" && x !== null;
}

export function isBlogImage(x: unknown): x is BlogImage {
  if (!isRecord(x)) return false;
  return typeof x.src === "string" && typeof x.alt === "string";
}

export function isBlogSEO(x: unknown): x is BlogSEO {
  if (!isRecord(x)) return false;
  if ("title" in x && x.title !== undefined && typeof x.title !== "string")
    return false;
  if (
    "description" in x &&
    x.description !== undefined &&
    typeof x.description !== "string"
  )
    return false;
  if (
    "keywords" in x &&
    x.keywords !== undefined &&
    !(
      Array.isArray(x.keywords) &&
      (x.keywords as unknown[]).every((k) => typeof k === "string")
    )
  )
    return false;
  if (
    "canonicalUrl" in x &&
    x.canonicalUrl !== undefined &&
    typeof x.canonicalUrl !== "string"
  )
    return false;
  if (
    "noindex" in x &&
    x.noindex !== undefined &&
    typeof x.noindex !== "boolean"
  )
    return false;
  return true;
}

export function isBlogAuthorSocial(x: unknown): x is BlogAuthorSocial {
  if (!isRecord(x)) return false;
  if (
    "twitter" in x &&
    x.twitter !== undefined &&
    typeof x.twitter !== "string"
  )
    return false;
  if (
    "linkedin" in x &&
    x.linkedin !== undefined &&
    typeof x.linkedin !== "string"
  )
    return false;
  if ("email" in x && x.email !== undefined && typeof x.email !== "string")
    return false;
  return true;
}

// Metadata type that uses Contentlayer BlogPost arrays directly
import type { BlogPost as ContentlayerBlogPost } from ".contentlayer/generated";

export interface BlogContentMetadata {
  totalPosts: number;
  totalCategories: number;
  totalTags: number;
  lastUpdated: string;
  featuredPosts: ContentlayerBlogPost[];
  popularPosts: ContentlayerBlogPost[];
  recentPosts: ContentlayerBlogPost[];
}

export interface BlogContentSearchResult {
  posts: ContentlayerBlogPost[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
