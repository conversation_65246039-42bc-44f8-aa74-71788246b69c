import { useMDXComponent } from "next-contentlayer2/hooks";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent } from "~/components/ui/card";
import { <PERSON><PERSON>, AlertDescription } from "~/components/ui/alert";
import { cn } from "~/lib/utils";
import {
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Lightbulb,
  Quote,
} from "lucide-react";

interface MDXContentProps {
  code: string;
}

// Custom components for MDX
const mdxComponents = {
  // Typography
  h1: ({ className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h1
      className={cn(
        "mb-4 mt-8 text-3xl font-bold tracking-tight text-primary first:mt-0",
        className,
      )}
      {...props}
    />
  ),
  h2: ({ className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h2
      className={cn(
        "mb-4 mt-8 border-b border-border pb-2 text-2xl font-bold tracking-tight text-primary",
        className,
      )}
      {...props}
    />
  ),
  h3: ({ className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h3
      className={cn(
        "mb-3 mt-6 text-xl font-semibold tracking-tight text-primary",
        className,
      )}
      {...props}
    />
  ),
  h4: ({ className, ...props }: React.HTMLAttributes<HTMLHeadingElement>) => (
    <h4
      className={cn(
        "mb-2 mt-4 text-lg font-semibold tracking-tight text-primary",
        className,
      )}
      {...props}
    />
  ),
  p: ({ className, ...props }: React.HTMLAttributes<HTMLParagraphElement>) => (
    <p
      className={cn(
        "mb-4 leading-7 text-foreground [&:not(:first-child)]:mt-4",
        className,
      )}
      {...props}
    />
  ),

  // Lists
  ul: ({ className, ...props }: React.HTMLAttributes<HTMLUListElement>) => (
    <ul
      className={cn("mb-4 ml-6 list-disc space-y-2 text-foreground", className)}
      {...props}
    />
  ),
  ol: ({ className, ...props }: React.HTMLAttributes<HTMLOListElement>) => (
    <ol
      className={cn(
        "mb-4 ml-6 list-decimal space-y-2 text-foreground",
        className,
      )}
      {...props}
    />
  ),
  li: ({ className, ...props }: React.HTMLAttributes<HTMLLIElement>) => (
    <li className={cn("leading-7", className)} {...props} />
  ),

  // Links
  a: ({
    className,
    href,
    ...props
  }: React.AnchorHTMLAttributes<HTMLAnchorElement>) => {
    const isExternal = href?.startsWith("http");
    const Component = isExternal ? "a" : Link;

    return (
      <Component
        href={href ?? "#"}
        className={cn(
          "font-medium text-primary underline underline-offset-4 transition-colors hover:text-primary/80",
          className,
        )}
        {...(isExternal && { target: "_blank", rel: "noopener noreferrer" })}
        {...props}
      />
    );
  },

  // Code
  code: ({ className, ...props }: React.HTMLAttributes<HTMLElement>) => (
    <code
      className={cn(
        "relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold text-foreground",
        className,
      )}
      {...props}
    />
  ),
  pre: ({ className, ...props }: React.HTMLAttributes<HTMLPreElement>) => (
    <pre
      className={cn(
        "mb-4 mt-6 overflow-x-auto rounded-lg border bg-muted p-4 font-mono text-sm",
        className,
      )}
      {...props}
    />
  ),

  // Blockquote
  blockquote: ({
    className,
    ...props
  }: React.HTMLAttributes<HTMLQuoteElement>) => (
    <blockquote
      className={cn(
        "mb-4 mt-6 border-l-4 border-primary pl-6 italic text-muted-foreground",
        className,
      )}
      {...props}
    />
  ),

  // Tables
  table: ({ className, ...props }: React.HTMLAttributes<HTMLTableElement>) => (
    <div className="mb-4 w-full overflow-y-auto">
      <table
        className={cn(
          "w-full border-collapse border border-border text-sm",
          className,
        )}
        {...props}
      />
    </div>
  ),
  th: ({ className, ...props }: React.HTMLAttributes<HTMLTableCellElement>) => (
    <th
      className={cn(
        "border border-border bg-muted px-4 py-2 text-left font-bold",
        className,
      )}
      {...props}
    />
  ),
  td: ({ className, ...props }: React.HTMLAttributes<HTMLTableCellElement>) => (
    <td
      className={cn("border border-border px-4 py-2", className)}
      {...props}
    />
  ),

  // Images
  img: ({
    className,
    alt,
    src,
    ...props
  }: React.ImgHTMLAttributes<HTMLImageElement>) => (
    <Image
      className={cn("rounded-lg border", className)}
      alt={alt ?? ""}
      src={src ?? ""}
      width={800}
      height={400}
      {...(props as Omit<
        React.ImgHTMLAttributes<HTMLImageElement>,
        "src" | "alt" | "width" | "height" | "loading" | "decoding"
      >)}
    />
  ),

  // Custom Components
  Badge,
  Card,
  CardContent,

  // Alert Components
  InfoBox: ({ children, ...props }: { children: React.ReactNode }) => (
    <Alert className="mb-4 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
      <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      <AlertDescription className="text-blue-800 dark:text-blue-200">
        {children}
      </AlertDescription>
    </Alert>
  ),

  WarningBox: ({ children, ...props }: { children: React.ReactNode }) => (
    <Alert className="mb-4 border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
      <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
      <AlertDescription className="text-yellow-800 dark:text-yellow-200">
        {children}
      </AlertDescription>
    </Alert>
  ),

  SuccessBox: ({ children, ...props }: { children: React.ReactNode }) => (
    <Alert className="mb-4 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
      <AlertDescription className="text-green-800 dark:text-green-200">
        {children}
      </AlertDescription>
    </Alert>
  ),

  ErrorBox: ({ children, ...props }: { children: React.ReactNode }) => (
    <Alert className="mb-4 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
      <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
      <AlertDescription className="text-red-800 dark:text-red-200">
        {children}
      </AlertDescription>
    </Alert>
  ),

  TipBox: ({ children, ...props }: { children: React.ReactNode }) => (
    <Alert className="mb-4 border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950">
      <Lightbulb className="h-4 w-4 text-purple-600 dark:text-purple-400" />
      <AlertDescription className="text-purple-800 dark:text-purple-200">
        {children}
      </AlertDescription>
    </Alert>
  ),

  QuoteBox: ({
    children,
    author,
    ...props
  }: {
    children: React.ReactNode;
    author?: string;
  }) => (
    <Card className="mb-4 border-l-4 border-l-primary">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <Quote className="mt-1 h-5 w-5 flex-shrink-0 text-primary" />
          <div>
            <div className="mb-2 italic text-foreground">{children}</div>
            {author && (
              <div className="text-sm font-medium text-muted-foreground">
                — {author}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  ),
};

export function MDXContent({ code }: MDXContentProps) {
  const Component = useMDXComponent(code);

  return (
    <div className="mdx-content">
      <Component components={mdxComponents} />
    </div>
  );
}
