"use client";

import { But<PERSON> } from "~/components/ui/button";
import { Share2, Bookmark } from "lucide-react";
import { safeShare } from "~/lib/browser-api";

interface BlogPostHeaderActionsProps {
  title: string;
}

export default function BlogPostHeaderActions({ title }: BlogPostHeaderActionsProps) {
  return (
    <div className="absolute right-4 top-4 flex gap-2">
      <Button
        size="sm"
        variant="secondary"
        className="bg-background/90 hover:bg-background"
        onClick={async () => {
          const result = await safeShare({ title, url: window.location.href });
          // TODO: toast based on result (shared/unsupported/failed)
        }}
      >
        <Share2 className="h-4 w-4" />
      </Button>
      <Button
        size="sm"
        variant="secondary"
        className="bg-background/90 hover:bg-background"
        onClick={() => {
          // Placeholder: bookmark implementation later
          // Intentionally non-async to avoid floating promises
          console.log("Bookmark clicked");
        }}
      >
        <Bookmark className="h-4 w-4" />
      </Button>
    </div>
  );
}

