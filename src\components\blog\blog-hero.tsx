import Link from "next/link";
import Image from "next/image";
import { type BlogPost as ContentlayerBlogPost } from ".contentlayer/generated";
import { formatPublishDate, formatReadingTime } from "~/lib/blog-utils";
import { Badge } from "~/components/ui/badge";
import { Clock, Calendar, User } from "lucide-react";

import { isBlogImage } from "~/types/content-mapped";

interface BlogHeroProps {
  featuredPost?: ContentlayerBlogPost;
  totalPosts: number;
}

export default function BlogHero({ featuredPost, totalPosts }: BlogHeroProps) {
  return (
    <section className="relative bg-gradient-to-br from-primary/5 via-background to-accent/5 py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          {/* Left Column - Hero Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <Badge variant="secondary" className="text-sm font-medium">
                NEET Blog
              </Badge>
              <h1 className="text-4xl font-bold leading-tight text-primary md:text-5xl lg:text-6xl">
                Master NEET with Expert Guidance
              </h1>
              <p className="text-lg leading-relaxed text-muted-foreground md:text-xl">
                Comprehensive study tips, exam strategies, and success stories
                from Aims Academy&apos;s expert faculty. Your complete guide to
                NEET 2025 success.
              </p>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap gap-6 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary md:text-3xl">
                  {totalPosts}+
                </div>
                <div className="text-sm text-muted-foreground">
                  Expert Articles
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary md:text-3xl">
                  5
                </div>
                <div className="text-sm text-muted-foreground">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary md:text-3xl">
                  100%
                </div>
                <div className="text-sm text-muted-foreground">
                  Free Content
                </div>
              </div>
            </div>

            {/* CTA Button */}
            <div className="pt-4">
              <Link
                href="#latest-articles"
                className="inline-flex items-center rounded-lg bg-primary px-6 py-3 font-medium text-primary-foreground transition-colors hover:bg-primary/90"
              >
                Start Reading
              </Link>
            </div>
          </div>

          {/* Right Column - Featured Post */}
          {featuredPost && (
            <div className="relative">
              <div className="overflow-hidden rounded-2xl border bg-card shadow-lg">
                {/* Featured Post Image */}
                {featuredPost.image && isBlogImage(featuredPost.image) && (
                  <div className="relative h-48 md:h-56">
                    <Image
                      src={featuredPost.image.src}
                      alt={featuredPost.image.alt}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 50vw"
                      priority
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                    <Badge
                      variant="secondary"
                      className="absolute left-4 top-4 bg-primary text-primary-foreground"
                    >
                      Featured
                    </Badge>
                  </div>
                )}

                {/* Featured Post Content */}
                <div className="space-y-4 p-6">
                  <div className="space-y-2">
                    <Badge variant="outline" className="text-xs">
                      {featuredPost.categorySlug
                        .replace("-", " ")
                        .toUpperCase()}
                    </Badge>
                    <h3 className="text-xl font-bold leading-tight text-card-foreground md:text-2xl">
                      <Link
                        href={featuredPost.url}
                        className="transition-colors hover:text-primary"
                      >
                        {featuredPost.title}
                      </Link>
                    </h3>
                    <p className="line-clamp-3 text-muted-foreground">
                      {featuredPost.excerpt}
                    </p>
                  </div>

                  {/* Post Meta */}
                  <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>{featuredPost.author}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>{formatPublishDate(featuredPost.publishedAt)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>{formatReadingTime(featuredPost.readingTime)}</span>
                    </div>
                  </div>

                  {/* Read More Link */}
                  <div className="pt-2">
                    <Link
                      href={featuredPost.url}
                      className="inline-flex items-center font-medium text-primary hover:underline"
                    >
                      Read Full Article →
                    </Link>
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -right-4 -top-4 h-24 w-24 rounded-full bg-primary/10 blur-xl" />
              <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-accent/10 blur-xl" />
            </div>
          )}
        </div>
      </div>

      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_theme(colors.primary)_1px,_transparent_0)] bg-[size:20px_20px]" />
      </div>
    </section>
  );
}
