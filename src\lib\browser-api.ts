/**
 * Typed helpers for safe browser API usage.
 * Use these from client components only, or behind isBrowser() checks.
 */

export function isBrowser(): boolean {
  return typeof window !== "undefined";
}

export function getNavigator(): Navigator | undefined {
  // Guard for SSR/SSG
  if (!isBrowser()) return undefined;
  try {
    return window.navigator;
  } catch {
    return undefined;
  }
}

export type ShareResult = "shared" | "unsupported" | "failed";

export async function safeShare(opts: {
  title?: string;
  text?: string;
  url?: string;
}): Promise<ShareResult> {
  const nav = getNavigator();
  const canShare = !!(nav && (nav as unknown as { share?: unknown }).share);
  if (!canShare) return "unsupported";
  try {
    await nav.share(opts);
    return "shared";
  } catch {
    return "failed";
  }
}

export async function copyToClipboard(text: string): Promise<boolean> {
  const nav = getNavigator();
  const clip = (
    nav as unknown as {
      clipboard?: { writeText?: (t: string) => Promise<void> };
    }
  ).clipboard?.writeText;
  if (!clip) return false;
  try {
    await clip(text);
    return true;
  } catch {
    return false;
  }
}

export function openPopup(
  url: string,
  features = "width=550,height=420",
): boolean {
  if (!isBrowser()) return false;
  try {
    const w = window.open(url, "_blank", features);
    return !!w;
  } catch {
    return false;
  }
}
