import Link from "next/link";
import {
  type BlogPost as ContentlayerBlogPost,
  type BlogCategory as ContentlayerBlogCategory,
  type Blog<PERSON>uthor as ContentlayerBlogAuthor,
} from ".contentlayer/generated";
import { formatPublishDate } from "~/lib/blog-utils";
import { isBlogAuthorSocial } from "~/types/content-mapped";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { Separator } from "~/components/ui/separator";
import {
  Share2,
  ThumbsUp,
  MessageCircle,
  Calendar,
  User,
  Mail,
  Twitter,
  Linkedin,
} from "lucide-react";
import { BlogPostShareButtons } from "./blog-post-share-buttons";

interface BlogPostFooterProps {
  post: ContentlayerBlogPost;
  category?: ContentlayerBlogCategory;
  author?: ContentlayerBlogAuthor;
}

export default function BlogPostFooter({
  post,
  category,
  author,
}: BlogPostFooterProps) {
  return (
    <footer className="mt-12 space-y-8">
      {/* Tags Section */}
      {post.tags && post.tags.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-foreground">Tags</h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge
                key={tag}
                variant="secondary"
                className="cursor-pointer transition-colors hover:bg-primary hover:text-primary-foreground"
              >
                #{tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      <Separator />

      {/* Social Share Section */}
      <div className="space-y-4">
        <h3 className="flex items-center gap-2 text-lg font-semibold text-foreground">
          <Share2 className="h-5 w-5 text-primary" />
          Share this article
        </h3>
        <BlogPostShareButtons post={post} />
      </div>

      <Separator />

      {/* Author Bio Section */}
      {author && (
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col gap-4 md:flex-row">
              {/* Author Avatar */}
              <div className="flex-shrink-0">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={author.avatar} alt={author.name} />
                  <AvatarFallback className="bg-primary text-lg text-primary-foreground">
                    {author.name.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>

              {/* Author Info */}
              <div className="flex-1 space-y-3">
                <div>
                  <h4 className="text-xl font-semibold text-foreground">
                    {author.name}
                  </h4>
                  <p className="text-sm font-medium text-primary">
                    {author.role}
                  </p>
                </div>

                <p className="leading-relaxed text-muted-foreground">
                  {author.bio}
                </p>

                {/* Author Social Links */}
                {isBlogAuthorSocial(author.social) && (
                  <div className="flex gap-3">
                    {author.social.email && (
                      <Button variant="outline" size="sm" asChild>
                        <a href={`mailto:${author.social.email}`}>
                          <Mail className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                    {author.social.twitter && (
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={author.social.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Twitter className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                    {author.social.linkedin && (
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={author.social.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Linkedin className="h-4 w-4" />
                        </a>
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Separator />

      {/* Article Meta Information */}
      <div className="space-y-3 rounded-lg bg-muted/50 p-4">
        <h4 className="font-semibold text-foreground">Article Information</h4>
        <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Published: {formatPublishDate(post.publishedAt)}</span>
            </div>
            {post.updatedAt && post.updatedAt !== post.publishedAt && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Updated: {formatPublishDate(post.updatedAt)}</span>
              </div>
            )}
            {author && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <User className="h-4 w-4" />
                <span>Author: {author.name}</span>
              </div>
            )}
          </div>
          <div className="space-y-2">
            {category && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <span>Category:</span>
                <Link href={`/blog/${category.slug}`}>
                  <Badge
                    variant="outline"
                    className="hover:bg-primary hover:text-primary-foreground"
                  >
                    {category.name}
                  </Badge>
                </Link>
              </div>
            )}
            <div className="flex items-center gap-2 text-muted-foreground">
              <span>Reading time: {post.readingTime} minutes</span>
            </div>
            <div className="flex items-center gap-2 text-muted-foreground">
              <span>Word count: {post.wordCount} words</span>
            </div>
          </div>
        </div>
      </div>

      {/* Engagement Section */}
      <div className="flex items-center justify-between rounded-lg bg-muted/30 p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <ThumbsUp className="h-4 w-4" />
            <span>Helpful</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MessageCircle className="h-4 w-4" />
            <span>Comment</span>
          </div>
        </div>
        <div className="text-sm text-muted-foreground">
          Was this article helpful?
        </div>
      </div>

      {/* Call to Action */}
      <Card className="bg-gradient-to-r from-primary/5 to-accent/5">
        <CardContent className="space-y-4 p-6 text-center">
          <h3 className="text-xl font-semibold text-foreground">
            Ready to Start Your NEET Journey?
          </h3>
          <p className="text-muted-foreground">
            Join thousands of students who have achieved their dreams with Aims
            Academy&apos;s expert guidance.
          </p>
          <div className="flex flex-col justify-center gap-3 sm:flex-row">
            <Button asChild>
              <Link href="/contact">Get Started Today</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/courses">Explore Courses</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </footer>
  );
}
