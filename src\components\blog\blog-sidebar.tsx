import Link from "next/link";
import Image from "next/image";
import {
  type BlogPost as ContentlayerBlogPost,
  type BlogCategory as ContentlayerBlogCategory,
} from ".contentlayer/generated";
import { BlogCategoriesCompact } from "./blog-categories";
import { formatPublishDate, formatReadingTime } from "~/lib/blog-utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { isBlogImage } from "~/types/content-mapped";
import {
  Clock,
  Calendar,
  TrendingUp,
  Search,
  Mail,
  BookOpen,
  Star,
} from "lucide-react";

interface BlogSidebarProps {
  categories: ContentlayerBlogCategory[];
  recentPosts: ContentlayerBlogPost[];
  popularPosts: ContentlayerBlogPost[];
  currentCategory?: string;
}

export default function BlogSidebar({
  categories,
  recentPosts,
  popularPosts,
  currentCategory,
}: BlogSidebarProps) {
  return (
    <div className="space-y-6">
      {/* Search Widget */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Search className="h-5 w-5 text-primary" />
            Search Articles
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form className="flex gap-2">
            <Input
              placeholder="Search for articles..."
              className="flex-1"
              name="search"
            />
            <Button type="submit" size="sm" className="px-3">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Categories Widget */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BookOpen className="h-5 w-5 text-primary" />
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <BlogCategoriesCompact
            categories={categories}
            currentCategory={currentCategory}
          />
        </CardContent>
      </Card>

      {/* Popular Posts Widget */}
      {popularPosts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <TrendingUp className="h-5 w-5 text-primary" />
              Popular Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {popularPosts.slice(0, 3).map((post, index) => (
                <div key={post.slug} className="group flex gap-3">
                  {/* Rank Number */}
                  <div className="flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-primary/10">
                    <span className="text-xs font-bold text-primary">
                      {index + 1}
                    </span>
                  </div>

                  {/* Post Content */}
                  <div className="min-w-0 flex-1">
                    <h4 className="line-clamp-2 text-sm font-medium leading-tight transition-colors group-hover:text-primary">
                      <Link href={post.url}>{post.title}</Link>
                    </h4>
                    <div className="mt-1 flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatPublishDate(post.publishedAt)}</span>
                      <Clock className="h-3 w-3" />
                      <span>{formatReadingTime(post.readingTime)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Posts Widget */}
      {recentPosts.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Clock className="h-5 w-5 text-primary" />
              Recent Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentPosts.slice(0, 4).map((post) => (
                <div key={post.slug} className="group flex gap-3">
                  {/* Post Image */}
                  {post.image && isBlogImage(post.image) ? (
                    <div className="relative h-12 w-16 flex-shrink-0 overflow-hidden rounded">
                      <Image
                        src={post.image.src}
                        alt={post.image.alt}
                        fill
                        className="object-cover transition-transform group-hover:scale-105"
                        sizes="64px"
                      />
                    </div>
                  ) : (
                    <div className="flex h-12 w-16 flex-shrink-0 items-center justify-center rounded bg-primary/10">
                      <span className="text-xs font-bold text-primary">
                        {post.title.charAt(0)}
                      </span>
                    </div>
                  )}

                  {/* Post Content */}
                  <div className="min-w-0 flex-1">
                    <h4 className="line-clamp-2 text-sm font-medium leading-tight transition-colors group-hover:text-primary">
                      <Link href={post.url}>{post.title}</Link>
                    </h4>
                    <div className="mt-1 flex items-center gap-2 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{formatPublishDate(post.publishedAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Newsletter Signup Widget */}
      <Card className="bg-gradient-to-br from-primary/5 to-accent/5">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Mail className="h-5 w-5 text-primary" />
            Stay Updated
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Get the latest NEET preparation tips and study strategies
              delivered to your inbox.
            </p>
            <div className="space-y-2">
              <Input
                placeholder="Enter your email"
                type="email"
                className="bg-background"
              />
              <Button className="w-full" size="sm">
                Subscribe Now
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              No spam. Unsubscribe anytime.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Study Tips Widget */}
      <Card className="bg-gradient-to-br from-accent/5 to-primary/5">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Star className="h-5 w-5 text-primary" />
            Quick Study Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <div className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary" />
                <p className="text-sm">
                  Create a daily study schedule and stick to it
                </p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary" />
                <p className="text-sm">
                  Practice previous year questions regularly
                </p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary" />
                <p className="text-sm">
                  Take mock tests to improve time management
                </p>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-2 h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary" />
                <p className="text-sm">
                  Focus on understanding concepts, not memorizing
                </p>
              </div>
            </div>
            <Link href="/blog/study-tips">
              <Button variant="outline" size="sm" className="w-full">
                More Study Tips →
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
