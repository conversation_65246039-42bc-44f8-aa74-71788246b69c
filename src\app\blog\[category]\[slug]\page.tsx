/* eslint-disable @typescript-eslint/prefer-nullish-coalescing */
import { type Metadata } from "next";

import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";
import { generateArticleSchema } from "~/lib/structured-data";
import Script from "next/script";
import { MDXContent } from "~/components/blog/mdx-content";
import BlogPostHeader from "~/components/blog/blog-post-header";
import BlogPostFooter from "~/components/blog/blog-post-footer";
import BlogSidebar from "~/components/blog/blog-sidebar";
import RelatedPosts from "~/components/blog/related-posts";
import { getBlogMetadata, getRelatedPosts } from "~/lib/blog-utils";
import {
  allBlogPosts,
  allBlogCategories,
  allBlogAuthors,
} from ".contentlayer/generated";
import {
  type BlogSEO,
  isBlogSEO,
  isBlogImage,
  isBlogAuthorSocial,
} from "~/types/content-mapped";

// ============================================================================
// Static Params Generation
// ============================================================================

export async function generateStaticParams() {
  return allBlogPosts.map((post) => ({
    category: post.categorySlug,
    slug: post.slug,
  }));
}

// ============================================================================
// Metadata Generation
// ============================================================================

export async function generateMetadata({
  params,
}: {
  params: Promise<{ category: string; slug: string }>;
}): Promise<Metadata> {
  const { category, slug } = await params;
  const post = allBlogPosts.find(
    (post) => post.categorySlug === category && post.slug === slug,
  );

  if (!post || post.draft) {
    return {
      title: "Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const categoryData = allBlogCategories.find((cat) => cat.slug === category);
  const author = allBlogAuthors.find((auth) => auth.slug === post.author);

  // Safely access SEO properties with runtime guard
  const seo = isBlogSEO(post.seo) ? post.seo : undefined;

  // Safely access image properties with runtime guard
  const image = isBlogImage(post.image) ? post.image : undefined;

  // Safely access author social properties with runtime guard
  const authorSocial = isBlogAuthorSocial(author?.social)
    ? author.social
    : undefined;

  // Use custom SEO title or generate from post title
  const seoTitle = seo?.title ?? `${post.title} | NEET Blog - Aims Academy`;

  // Use custom SEO description or post description (truncated to 150 chars)
  const seoDescription =
    seo?.description ||
    (post.description.length > 147
      ? post.description.substring(0, 147) + "..."
      : post.description);

  return {
    title: seoTitle,
    description: seoDescription,
    keywords: [
      ...post.tags,
      ...(seo?.keywords || []),
      "NEET Preparation",
      "Medical Entrance",
      "Aims Academy",
      categoryData?.name || "",
    ].filter(Boolean),
    alternates: {
      canonical: seo?.canonicalUrl || `${SITE_DOMAIN}${post.url}`,
    },
    robots: {
      index: !seo?.noindex,
      follow: !seo?.noindex,
    },
    openGraph: {
      title: post.title,
      description: seoDescription,
      url: `${SITE_DOMAIN}${post.url}`,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: image?.src || "/blog/default-post-image.jpg",
          width: 1200,
          height: 630,
          alt: image?.alt || post.title,
        },
      ],
      locale: "en_IN",
      type: "article",
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt || post.publishedAt,
      authors: [author?.name || post.author],
      tags: post.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: seoDescription,
      images: [image?.src || "/blog/default-post-image.jpg"],
      creator: authorSocial?.twitter,
    },
  };
}

// ============================================================================
// Blog Post Page Component
// ============================================================================

export default async function BlogPostPage({
  params,
}: {
  params: Promise<{ category: string; slug: string }>;
}) {
  const { category, slug } = await params;

  // Find the blog post
  const post = allBlogPosts.find(
    (post) => post.categorySlug === category && post.slug === slug,
  );

  if (!post || post.draft) {
    return <div>Post not found</div>;
  }

  // At this point, post is guaranteed to be defined due to the check above

  // Get related data
  const categoryData = allBlogCategories.find((cat) => cat.slug === category);
  const author = allBlogAuthors.find((auth) => auth.slug === post.author);
  const allCategories = allBlogCategories;

  // Get related posts
  const relatedPosts = getRelatedPosts(post, allBlogPosts, 3);

  // Get recent posts for sidebar
  const recentPosts = allBlogPosts
    .filter((p) => !p.draft && p.slug !== post.slug)
    .sort(
      (a, b) =>
        new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime(),
    )
    .slice(0, 5);

  // Get blog metadata
  const blogMetadata = getBlogMetadata(allBlogPosts.filter((p) => !p.draft));

  // Safely access image properties with runtime guard
  const image = isBlogImage(post.image) ? post.image : undefined;

  // Generate structured data
  const articleSchema = generateArticleSchema({
    title: post.title,
    description: post.description,
    url: `${SITE_DOMAIN}${post.url}`,
    datePublished: post.publishedAt,
    dateModified: post.updatedAt || post.publishedAt,
    authorName: author?.name || post.author,
    authorBio: author?.bio,
    category: categoryData?.name || category,
    tags: post.tags,
    image: image?.src,
    wordCount: post.wordCount,
    readingTime: post.readingTime,
  });

  return (
    <>
      <article className="min-h-screen bg-background">
        {/* Blog Post Header */}
        <BlogPostHeader post={post} category={categoryData} author={author} />

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
            {/* Main Content */}
            <div className="lg:col-span-3">
              {/* Post Content */}
              <div className="prose prose-lg max-w-none">
                <MDXContent code={post.body.code} />
              </div>

              {/* Post Footer */}
              <BlogPostFooter
                post={post}
                author={author}
                category={categoryData}
              />

              {/* Related Posts */}
              {relatedPosts.length > 0 && (
                <div className="mt-16">
                  <RelatedPosts posts={relatedPosts} />
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <BlogSidebar
                categories={allCategories}
                recentPosts={recentPosts}
                popularPosts={blogMetadata.popularPosts}
                currentCategory={category}
              />
            </div>
          </div>
        </div>
      </article>

      {/* Structured Data */}
      <Script
        id="article-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(articleSchema),
        }}
      />
    </>
  );
}
